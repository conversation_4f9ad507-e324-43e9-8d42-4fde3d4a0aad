# MCP服务器安装使用说明

头儿，我已经成功为您安装了6个MCP服务器！以下是详细的安装状态和使用说明：

## 安装状态

### ✅ 已成功安装的MCP服务器：

1. **Playwright MCP** - 前端自动化测试工具
   - 状态：✅ 安装成功
   - 用途：用于前端测试、页面操作、截图等

2. **Sequential Thinking MCP** - 分步骤思维工具  
   - 状态：✅ 安装成功
   - 用途：复杂任务分解、决策支持、问题诊断

3. **MySQL MCP** - 数据库操作工具
   - 状态：✅ 安装成功
   - 用途：数据库管理、查询、数据验证
   - 配置：已配置连接到本地MySQL (127.0.0.1:3306, 数据库: aigcview)

4. **Context7 MCP** - 文档上下文集成工具
   - 状态：✅ 安装成功
   - 用途：获取最新API文档、最佳实践、技术栈更新

5. **Browser Tools MCP** - 浏览器工具
   - 状态：✅ 安装成功
   - 用途：页面截图、调试支持、性能分析

6. **Alipay MCP** - 支付宝服务器
   - 状态：⚠️ 需要配置环境变量
   - 用途：支付宝支付功能集成

## 配置说明

### Alipay MCP 环境变量配置

Alipay MCP需要以下环境变量，您已经在配置中提供了：

```bash
AP_APP_ID=2021005179622178
AP_APP_KEY=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC2WyRKC/pxbb7f+/uqrbCM6rmUjrpmz9m63ZkORqseN5P4mpNqfmNxbjNmNHcwDvcLECLNx8/VoE2wsZe+JV/Z0gRSRYTDqdOnmx3dn5LSCT7OTIPsvhCp0hEfTC6/2mQWOn1J7VdTy9E3ql1PLNR3h7n8FC1mG5vHfXurt8rAVDh1RNw0spFI9VkB3qyy1btEAgOBdYF56bRJcs3MX+1BKNCY9kpRh3dyb7vVkSDJZTeMcgrQjkzkfF9vZvPu3b6CDicwwpiN9kXisoqX7mlopjUEoHMm991hy+ulxxDeQczhGSYT2gJCsfrNTdxdHt6UqILo8Liy1crAZGTvBa4zAgMBAAECggEAP9gfF3GkKExVFJRjYzlWY6/Wc4WZC1/gCgWFjbL+PCEXRGXSEOnZJSCBDFp4FDJZGd13YfZ/9HogWSmESAVLzrYx3yAJwHtX6Alt5mb0/2ZPP+kYEy3kJaCvb/a7M15hxkqKDPT0qxjj0gpqisrWRFcXrYLe9i3aV3i+cLtzV+xL/tThIh/7KSpNdaeIUAVU5VHxU5Xfvr+GuWzwEXHbbznHJJocxufpwn8M2biPlsXdKhhZouL0kLQRLld0zvhhTw/61ekmS9kIPOId7ArbgdfmTCbpYU2i5kjkIQkj/PVeZDTb4aJeQOeEKhnJOZQrVlFBFJdjzWddH/0TaC3Y4QKBgQD3U3zIC2lGyaXNwNbb3yoX0/dTQ+0/AAZcGEVjYCB5mMa+Aax0RoraDkJclid6Q0RKcBENmHCvJc5lQXM4N3JtJjSr/YfV385RuQnFz+mx1CDcjRSt7on5HGrNTmuWFh5MwrQU8FBrJLrntE8tCu2lQCovy3zmgxCwAoQdhNbtcQKBgQC8wFkOAKO/7J96S7Ji1XWma6XMcI0v+1opmbwIF0au1YtzQ1yb+yiFk9TWI/Yrv1CfouT7FebAae8raUonlCZDaE8OWj8CTzG3HKhB84Ll39aIybGIApW4ZvE6+KFjzAooo/nkBmz+8S7cAhFKkXiV9k9FSwj/mS41sYAn8A3T4wKBgQCuGyiRTm8q9NLyd9kI6R2hrobCJ+RAYE0Z0cAvB8F0uBbYNSdopywh+r2+nblmuSm+2Qs4LeLNeWjFIQDyRx4XNjkIBi70YVvFbeQyS32wwKrtd6diFbUtF1Mb+iOEgUeNM9c/kjiaZD3q1KSUCchndzb7dF0VjJhXvz2v5g7DEQKBgGVxIuuJoHgHnfZGNIs1mdNlS2hCnT7KYby16P6YM97G2fmKnH0gULjpqJumHCmK2rPRAiuHq8qBpI66OaZn0uCrcMGP8wk0ss1s02kOjK0qzcwaShWE/h5rBP7inSpbgKIy7pyEb5Pc34hSfCSNZjeCdzwQhbeJBfLJgMz2ERhtAoGBAIzG3y8ZwdZX+3dSb2bsfxiByA1lxDbpZGUKz0jk/B6MEgqMwDmBtalT+Y5jBUdvpNBexpGQ9bRKexZ5pDh1Fq0XoPcesIRNqDqkDTS8NGjsqsU9mlmQu7RFYWZbNcIyyDw+A8aLweMF7kG1wt7/RA7u9WCfxu4EhFwETy/gZ46l
AP_PUB_KEY=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyqRCUsAfnioBBMK5lbyRTuPHTu1/cMP94OAX0jDabg3VOaItw6/Cnahu5SdMq7XBU2XKd1hGG/DF7Bx5oqA1fWSKZAP6b7UHfjjsLNmtIl+nVGNJiMI7bjHmzFF/CaKXBgONoBVAjTRza6WkpEY00vqJmbQ7yzd+Zk+px7dPNC0Z4GCTY2YBtp1RF3iDIE7WcL/B8JhZwxqzyibthrYAu0eGYGL97O74z7Mtut3cLDlM9/Gw2UYG0X/dHPoGVJE9kN7T9lRUy8zLTaW47In/O91T6uhBrVTYK8M8eDp5ldXCc1S+FeYAgGEtp8tdY3h2o793oRAxf5PYePJtdezf3QIDAQAB
```

### MySQL MCP 配置

已配置连接参数：
- 主机：127.0.0.1:3306
- 用户：root
- 密码：root
- 数据库：aigcview

## 使用方法

### 1. 在Claude Desktop中配置

将 `mcp-config.json` 中的配置添加到您的Claude Desktop配置文件中。

### 2. 重启Claude Desktop

配置完成后重启Claude Desktop以加载MCP服务器。

### 3. 验证安装

重启后，您应该能够在Claude中使用这些工具：

- 🎭 **Playwright**: 自动化测试和页面操作
- 🧠 **Sequential Thinking**: 复杂问题分解
- 🗄️ **MySQL**: 数据库操作
- 📚 **Context7**: 获取最新文档
- 🌐 **Browser Tools**: 浏览器调试
- 💰 **Alipay**: 支付功能（需要正确配置环境变量）

## 注意事项

1. **MySQL连接**: 确保MySQL服务正在运行，且数据库`aigcview`存在
2. **支付宝配置**: Alipay MCP需要有效的支付宝开发者账号和密钥
3. **网络访问**: 某些工具可能需要网络访问权限
4. **端口占用**: Browser Tools会自动寻找可用端口

## 故障排除

如果某个MCP服务器无法正常工作：

1. 检查配置文件格式是否正确
2. 确认所需的环境变量已设置
3. 查看Claude Desktop的日志文件
4. 重启Claude Desktop

---

**安装完成！** 🎉

头儿，您的MCP工具集已经准备就绪。这些工具将大大提升您的开发效率，特别是在Web出海项目的开发和运营中。有任何问题随时告诉我！
