<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智界AIGC - AI插件商城</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        /* --- 全局和基礎樣式 --- */
        :root {
            --primary-blue: #446dff;
            --primary-green: #00c875;
            --dark-bg: #1e2029;
            --main-bg: #f7f8fa;
            --card-bg: #ffffff;
            --text-dark: #212529;
            --text-light: #6c757d;
            --text-white: #ffffff;
            --border-color: #e9ecef;
            --red-tag: #ff4d4f;
        }

        body, html {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft JhengHei", "Helvetica Neue", Arial, sans-serif;
            background-color: var(--main-bg);
            color: var(--text-dark);
            font-size: 14px;
        }

        .page-container {
            width: 100%;
        }

        /* --- 頭部 Header --- */
        .main-header {
            background-color: var(--dark-bg);
            padding: 0 24px;
            color: var(--text-white);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
            height: 60px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 20px;
            font-weight: bold;
        }

        .logo i {
            font-size: 24px;
            margin-right: 8px;
            color: var(--primary-blue);
        }

        .main-nav {
            display: flex;
            gap: 20px;
        }

        .main-nav a {
            color: var(--text-white);
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 6px;
            transition: background-color 0.2s;
        }

        .main-nav a.active, .main-nav a:hover {
            background-color: var(--primary-blue);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .header-actions a {
            color: var(--text-white);
            text-decoration: none;
        }
        
        .btn-register {
            background-color: var(--primary-green);
            color: var(--text-white);
            padding: 8px 20px;
            border-radius: 16px;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: opacity 0.2s;
        }

        .btn-register:hover {
            opacity: 0.9;
        }

        /* --- Hero 區域 --- */
        .hero-section {
            background-color: var(--dark-bg);
            padding: 40px 24px;
            color: var(--text-white);
            border-bottom: 1px solid #333;
        }

        .hero-content {
            max-width: 1400px;
            margin: 0 auto;
        }

        .hero-content h1 {
            font-size: 28px;
            margin: 0 0 8px 0;
        }

        .hero-content p {
            font-size: 14px;
            color: var(--text-light);
            margin: 0;
        }
        
        /* --- 搜索欄 --- */
        .search-section {
            background-color: var(--card-bg);
            padding: 24px;
            border-bottom: 1px solid var(--border-color);
        }

        .search-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
        }
        
        .search-input-wrapper {
            position: relative;
        }
        
        .search-input-wrapper i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
        }
        
        .search-input {
            width: 500px;
            height: 44px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 0 16px 0 40px;
            font-size: 14px;
        }
        
        .btn-search {
            background-color: var(--primary-blue);
            color: var(--text-white);
            height: 44px;
            padding: 0 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }

        /* --- 主內容區域 --- */
        .main-content {
            display: flex;
            max-width: 1400px;
            margin: 24px auto;
            padding: 0 24px;
            gap: 24px;
        }

        /* --- 側邊欄 --- */
        .sidebar {
            flex-shrink: 0;
            width: 220px;
            background-color: var(--card-bg);
            padding: 16px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
            align-self: flex-start;
        }

        .sidebar-title {
            font-size: 12px;
            color: var(--text-light);
            margin-bottom: 16px;
            padding: 0 8px;
        }

        .category-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .category-item {
            display: flex;
            align-items: center;
            padding: 12px 8px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s, color 0.2s;
        }
        
        .category-item:hover {
            background-color: #f0f3ff;
        }

        .category-item.active {
            background-color: var(--primary-blue);
            color: var(--text-white);
        }
        
        .category-item i {
            width: 24px;
            text-align: center;
            margin-right: 12px;
            font-size: 16px;
        }

        /* --- 插件列表 --- */
        .plugin-area {
            flex-grow: 1;
        }

        .plugin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .plugin-header h2 {
            font-size: 20px;
            margin: 0;
        }

        .plugin-header .filter-info {
            color: var(--text-light);
            font-size: 12px;
        }

        .plugin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 24px;
        }

        .plugin-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .plugin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.1);
        }

        .plugin-card-image {
            width: 100%;
            height: 160px;
            object-fit: cover;
            position: relative;
        }

        .plugin-card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .card-label {
            position: absolute;
            top: 12px;
            left: -1px;
            padding: 4px 12px 4px 8px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            border-radius: 0 12px 12px 0;
        }
        
        .card-label.red {
            background-color: var(--red-tag);
        }

        .card-label.blue {
            background-color: var(--primary-blue);
        }
        
        .card-label i {
            margin-right: 4px;
        }

        .plugin-card-content {
            padding: 16px;
        }
        
        .plugin-card-content h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
        }
        
        .plugin-card-content p {
            margin: 0 0 16px 0;
            font-size: 12px;
            color: var(--text-light);
            height: 18px;
        }
        
        .plugin-card-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .plugin-price {
            font-size: 16px;
            font-weight: bold;
            color: var(--red-tag);
        }

        .plugin-price span {
            font-size: 12px;
            color: var(--text-light);
            font-weight: normal;
        }

        .btn-select {
            background-color: var(--primary-blue);
            color: var(--text-white);
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }
    </style>
</head>
<body>

    <div class="page-container">
        <header class="main-header">
            <div class="nav-container">
                <div class="logo">
                    <i class="fa-solid fa-shapes"></i>
                    <span>智界AIGC</span>
                </div>
                <nav class="main-nav">
                    <a href="#">首頁</a>
                    <a href="#" class="active">用戶中心</a>
                    <a href="#">我的插件</a>
                    <a href="#">合作諮詢</a>
                    <a href="#">價格</a>
                    <a href="#">個人中心</a>
                </nav>
                <div class="header-actions">
                    <a href="#">合作諮詢</a>
                    <button class="btn-register">登錄/註冊</button>
                </div>
            </div>
        </header>

        <section class="hero-section">
            <div class="hero-content">
                <h1>AI插件商城</h1>
                <p>數據驅動的AI插件，提升你的指令響應和解決問題能力</p>
            </div>
        </section>

        <section class="search-section">
            <div class="search-container">
                <div class="search-input-wrapper">
                    <i class="fa-solid fa-magnifying-glass"></i>
                    <input type="text" class="search-input" placeholder="搜索插件名稱、描述...">
                </div>
                <button class="btn-search">搜索插件</button>
            </div>
        </section>

        <div class="main-content">
            <aside class="sidebar">
                <div class="sidebar-title">插件分類</div>
                <ul class="category-list">
                    <li class="category-item active"><i class="fa-solid fa-layer-group"></i> 全部</li>
                    <li class="category-item"><i class="fa-solid fa-file-alt"></i> 文本</li>
                    <li class="category-item"><i class="fa-solid fa-image"></i> 圖片</li>
                    <li class="category-item"><i class="fa-solid fa-palette"></i> AI作畫</li>
                    <li class="category-item"><i class="fa-solid fa-mug-hot"></i> 生活</li>
                    <li class="category-item"><i class="fa-solid fa-music"></i> 音樂</li>
                    <li class="category-item"><i class="fa-solid fa-video"></i> 視頻</li>
                    <li class="category-item"><i class="fa-solid fa-briefcase"></i> 辦公</li>
                    <li class="category-item"><i class="fa-solid fa-wrench"></i> 工具</li>
                    <li class="category-item"><i class="fa-solid fa-ellipsis-h"></i> 其他</li>
                </ul>
            </aside>
            <main class="plugin-area">
                <div class="plugin-header">
                    <h2>全部插件</h2>
                    <span class="filter-info">4個應用</span>
                </div>
                <div class="plugin-grid">
                    <div class="plugin-card">
                        <div class="plugin-card-image">
                            <img src="https://placehold.co/400x220/2a2a2a/ffffff?text=Plugin" alt="可可羅">
                        </div>
                        <div class="plugin-card-content">
                            <h3>可可羅</h3>
                            <p>小紅書爆款標題</p>
                            <div class="plugin-card-footer">
                                <div class="plugin-price">¥1.00 <span>/次</span></div>
                                <button class="btn-select">選擇插件</button>
                            </div>
                        </div>
                    </div>
                    <div class="plugin-card">
                        <div class="plugin-card-image">
                            <img src="https://placehold.co/400x220/333333/ffffff?text=Plugin" alt="小紅書發布">
                             <div class="card-label red">
                                <i class="fa-brands fa-hotjar"></i> 小紅書
                            </div>
                        </div>
                        <div class="plugin-card-content">
                            <h3>小紅書一鍵發布</h3>
                            <p>A simple App to test</p>
                            <div class="plugin-card-footer">
                                <div class="plugin-price">¥20.00 <span>/次</span></div>
                                <button class="btn-select">選擇插件</button>
                            </div>
                        </div>
                    </div>
                    <div class="plugin-card">
                        <div class="plugin-card-image">
                           <img src="https://placehold.co/400x220/1a3a6a/ffffff?text=AI" alt="AI潤文">
                             <div class="card-label blue">
                                <i class="fa-solid fa-wrench"></i> 工具
                            </div>
                        </div>
                        <div class="plugin-card-content">
                            <h3>AI潤文</h3>
                            <p>文章潤色</p>
                            <div class="plugin-card-footer">
                                <div class="plugin-price">¥101.00 <span>/次</span></div>
                                <button class="btn-select">選擇插件</button>
                            </div>
                        </div>
                    </div>
                    <div class="plugin-card">
                        <div class="plugin-card-image">
                            <img src="https://placehold.co/400x220/4a2a4a/ffffff?text=Plugin" alt="另一個插件">
                        </div>
                        <div class="plugin-card-content">
                            <h3>創意圖生成</h3>
                            <p>根據描述生成圖片</p>
                            <div class="plugin-card-footer">
                                <div class="plugin-price">¥5.00 <span>/次</span></div>
                                <button class="btn-select">選擇插件</button>
                            </div>
                        </div>
                    </div>

                </div>
            </main>
        </div>
    </div>

</body>
</html>