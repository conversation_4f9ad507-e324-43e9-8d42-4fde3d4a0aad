<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智界AIGC - AI插件商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 64px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 20px;
            font-weight: bold;
            color: #4f46e5;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .nav-links {
            display: flex;
            gap: 32px;
            list-style: none;
        }

        .nav-link {
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .nav-link:hover {
            background: rgba(79, 70, 229, 0.1);
            color: #4f46e5;
        }

        .nav-link.active {
            background: #4f46e5;
            color: white;
        }

        .header-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-secondary {
            background: rgba(148, 163, 184, 0.1);
            color: #64748b;
            border: 1px solid rgba(148, 163, 184, 0.3);
        }

        .btn-primary {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        /* 主要内容区域 */
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }

        .hero-title {
            font-size: 56px;
            font-weight: 700;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #ffffff, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 20px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 40px;
            font-weight: 300;
        }

        .search-bar {
            display: flex;
            max-width: 600px;
            margin: 0 auto 50px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .search-input {
            flex: 1;
            padding: 16px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            background: transparent;
            outline: none;
        }

        .search-input::placeholder {
            color: #94a3b8;
        }

        .search-btn {
            padding: 16px 24px;
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .search-btn:hover {
            transform: scale(1.05);
        }

        /* 内容区域 */
        .content-area {
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 40px;
        }

        /* 侧边栏 */
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            height: fit-content;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 24px;
            color: #1e293b;
        }

        .category-list {
            list-style: none;
        }

        .category-item {
            margin-bottom: 8px;
        }

        .category-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 14px 16px;
            text-decoration: none;
            color: #64748b;
            border-radius: 12px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .category-link:hover {
            background: rgba(79, 70, 229, 0.1);
            color: #4f46e5;
            transform: translateX(4px);
        }

        .category-link.active {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .category-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .category-link.active .category-icon {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 插件列表 */
        .plugin-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
        }

        .section-stats {
            color: #64748b;
            font-size: 16px;
            font-weight: 500;
        }

        .plugin-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
        }

        .plugin-card {
            background: white;
            border-radius: 16px;
            padding: 0;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(226, 232, 240, 0.8);
            overflow: hidden;
            position: relative;
        }

        .plugin-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .plugin-image {
            width: 100%;
            height: 180px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .plugin-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        }

        .plugin-content {
            padding: 20px;
        }

        .plugin-label {
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            z-index: 2;
        }

        .plugin-name {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            color: #1e293b;
        }

        .plugin-description {
            color: #64748b;
            margin-bottom: 16px;
            font-size: 14px;
            line-height: 1.5;
        }

        .plugin-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .tag {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            color: #475569;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .plugin-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .plugin-price {
            font-size: 18px;
            font-weight: 700;
            color: #dc2626;
        }

        .plugin-btn {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        .plugin-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 16px rgba(79, 70, 229, 0.4);
        }

        /* 特殊插件卡片样式 */
        .plugin-card.featured .plugin-image {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }

        .plugin-card.ai-art .plugin-image {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #2d3748;
        }

        .plugin-card.text-tool .plugin-image {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        @media (max-width: 768px) {
            .content-area {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .hero-title {
                font-size: 36px;
            }

            .search-bar {
                flex-direction: column;
                gap: 12px;
            }

            .plugin-grid {
                grid-template-columns: 1fr;
            }

            .main-container {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <div class="logo-icon">智</div>
                <span>智界AIGC</span>
            </div>
            
            <nav>
                <ul class="nav-links">
                    <li><a href="/home" class="nav-link">首页</a></li>
                    <li><a href="/user-center" class="nav-link active">插件商城</a></li>
                    <li><a href="/my-plugins" class="nav-link">我的插件</a></li>
                    <li><a href="/cooperation" class="nav-link">开发者</a></li>
                    <li><a href="/pricing" class="nav-link">价格</a></li>
                    <li><a href="/profile" class="nav-link">个人中心</a></li>
                </ul>
            </nav>
            
            <div class="header-actions">
                <a href="#" class="btn btn-secondary">合作咨询</a>
                <a href="#" class="btn btn-primary">登录/注册</a>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-container">
        <!-- 英雄区域 -->
        <section class="hero-section">
            <h1 class="hero-title">AI插件商城</h1>
            <p class="hero-subtitle">数据驱动的AI插件，提升你的指令响应和解决问题能力</p>
        </section>

        <!-- 搜索栏 -->
        <div class="search-bar">
            <input type="text" class="search-input" placeholder="搜索插件名称、描述">
            <button class="search-btn">搜索插件</button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <h3 class="sidebar-title">插件分类</h3>
                <ul class="category-list">
                    <li class="category-item">
                        <a href="#" class="category-link active">
                            <div class="category-icon">📋</div>
                            <span>全部插件</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">📝</div>
                            <span>文本</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">🖼️</div>
                            <span>图片</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">🎨</div>
                            <span>AI作画</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">🏠</div>
                            <span>生活</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">🎵</div>
                            <span>音乐</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">🎬</div>
                            <span>视频</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">💼</div>
                            <span>办公</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">🔧</div>
                            <span>工具</span>
                        </a>
                    </li>
                    <li class="category-item">
                        <a href="#" class="category-link">
                            <div class="category-icon">📦</div>
                            <span>其他</span>
                        </a>
                    </li>
                </ul>
            </aside>

            <!-- 插件列表 -->
            <section class="plugin-section">
                <div class="section-header">
                    <h2 class="section-title">全部插件</h2>
                    <span class="section-stats">4个应用</span>
                </div>

                <div class="plugin-grid">
                    <!-- 可可罗插件 -->
                    <div class="plugin-card text-tool">
                        <div class="plugin-image">
                            <div style="font-size: 48px;">🤖</div>
                        </div>
                        <div class="plugin-content">
                            <h3 class="plugin-name">可可罗</h3>
                            <p class="plugin-description">小红书爆款标题生成器，帮你创造吸引眼球的标题</p>
                            <div class="plugin-tags">
                                <span class="tag">文本</span>
                                <span class="tag">营销</span>
                            </div>
                            <div class="plugin-footer">
                                <span class="plugin-price">¥1.00/次</span>
                                <button class="plugin-btn">选择</button>
                            </div>
                        </div>
                    </div>

                    <!-- 小红书发布插件 -->
                    <div class="plugin-card featured">
                        <div class="plugin-image">
                            <div style="font-size: 48px;">📱</div>
                        </div>
                        <div class="plugin-label" style="background-color: #FF2442;">小红书</div>
                        <div class="plugin-content">
                            <h3 class="plugin-name">小红书发布</h3>
                            <p class="plugin-description">一键发布内容到小红书平台，简化发布流程</p>
                            <div class="plugin-tags">
                                <span class="tag">工具</span>
                                <span class="tag">社交</span>
                            </div>
                            <div class="plugin-footer">
                                <span class="plugin-price">¥20.00/次</span>
                                <button class="plugin-btn">选择</button>
                            </div>
                        </div>
                    </div>

                    <!-- AI润文插件 -->
                    <div class="plugin-card ai-art">
                        <div class="plugin-image">
                            <div style="font-size: 48px;">✨</div>
                        </div>
                        <div class="plugin-content">
                            <h3 class="plugin-name">AI润文</h3>
                            <p class="plugin-description">智能文章润色工具，提升文章质量和可读性</p>
                            <div class="plugin-tags">
                                <span class="tag">文本</span>
                                <span class="tag">办公</span>
                            </div>
                            <div class="plugin-footer">
                                <span class="plugin-price">¥101.00/次</span>
                                <button class="plugin-btn">选择</button>
                            </div>
                        </div>
                    </div>

                    <!-- 新增第四个插件 -->
                    <div class="plugin-card">
                        <div class="plugin-image">
                            <div style="font-size: 48px;">🎨</div>
                        </div>
                        <div class="plugin-content">
                            <h3 class="plugin-name">AI绘画大师</h3>
                            <p class="plugin-description">专业AI绘画工具，支持多种艺术风格创作</p>
                            <div class="plugin-tags">
                                <span class="tag">AI作画</span>
                                <span class="tag">创意</span>
                            </div>
                            <div class="plugin-footer">
                                <span class="plugin-price">¥5.00/次</span>
                                <button class="plugin-btn">选择</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 分类切换
            const categoryLinks = document.querySelectorAll('.category-link');
            categoryLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    categoryLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 搜索功能
            const searchBtn = document.querySelector('.search-btn');
            const searchInput = document.querySelector('.search-input');
            
            searchBtn.addEventListener('click', function() {
                const query = searchInput.value.trim();
                if (query) {
                    alert('搜索功能：' + query);
                }
            });

            // 插件选择
            const pluginBtns = document.querySelectorAll('.plugin-btn');
            pluginBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const pluginName = this.closest('.plugin-card').querySelector('.plugin-name').textContent;
                    alert('选择了插件：' + pluginName);
                });
            });
        });
    </script>
</body>
</html>
