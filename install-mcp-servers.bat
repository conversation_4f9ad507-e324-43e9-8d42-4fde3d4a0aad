@echo off
echo 正在安装MCP服务器...
echo.

echo [1/6] 安装 Playwright MCP...
npx @playwright/mcp@latest --help
if %errorlevel% neq 0 (
    echo Playwright MCP 安装失败
) else (
    echo Playwright MCP 安装成功
)
echo.

echo [2/6] 安装 Sequential Thinking MCP...
npx -y @modelcontextprotocol/server-sequential-thinking --help
if %errorlevel% neq 0 (
    echo Sequential Thinking MCP 安装失败
) else (
    echo Sequential Thinking MCP 安装成功
)
echo.

echo [3/6] 安装 MySQL MCP...
npx -y @f4ww4z/mcp-mysql-server --help
if %errorlevel% neq 0 (
    echo MySQL MCP 安装失败
) else (
    echo MySQL MCP 安装成功
)
echo.

echo [4/6] 安装 Context7 MCP...
npx -y @upstash/context7-mcp --help
if %errorlevel% neq 0 (
    echo Context7 MCP 安装失败
) else (
    echo Context7 MCP 安装成功
)
echo.

echo [5/6] 安装 Browser Tools MCP...
npx @agentdeskai/browser-tools-mcp@latest --help
if %errorlevel% neq 0 (
    echo Browser Tools MCP 安装失败
) else (
    echo Browser Tools MCP 安装成功
)
echo.

echo [6/6] 安装 Alipay MCP...
npx -y @alipay/mcp-server-alipay --help
if %errorlevel% neq 0 (
    echo Alipay MCP 安装失败
) else (
    echo Alipay MCP 安装成功
)
echo.

echo 所有MCP服务器安装完成！
echo.
echo 配置信息已保存到 mcp-config.json
pause
