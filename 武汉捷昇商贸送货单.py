#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
武汉捷昇商贸有限公司送货单Excel生成器
"""

import pandas as pd
from datetime import datetime
import os

def create_delivery_note_excel():
    """创建送货单Excel文件"""
    
    # 公司信息
    company_info = {
        '公司名称': '武汉捷昇商贸有限公司',
        '送货单号': '赣C0772J',
        '单据编号': '25050716',
        '送货日期': '2025年7月17日',
        '司机电话': '15973029262'
    }
    
    # 收货单位信息
    receiver_info = {
        '收货单位': '湖北宸辰安全科技有限公司',
        '收货地址': '湖北省红安县经济开发区新型产业园厂八路'
    }
    
    # 货物明细数据
    goods_data = [
        {
            '钢卷号': '',
            '品名': '无花镀锌卷',
            '厂家': '攀华',
            '牌号': 'DX51D+Z',
            '规格型号': '0.75*1250*C',
            '数量(张)': '',
            '数量(卷)': 2,
            '重量(吨)': 10.490,
            '单价(元)': 0.00,
            '金额': 0.00,
            '备注': '分800+50*4+125*2'
        },
        {
            '钢卷号': '',
            '品名': '无花镀锌卷',
            '厂家': '攀华',
            '牌号': 'DX51D+Z',
            '规格型号': '0.70*1250*C',
            '数量(张)': '',
            '数量(卷)': 1,
            '重量(吨)': 7.790,
            '单价(元)': 0.00,
            '金额': 0.00,
            '备注': '分650+125*4+50*2'
        },
        {
            '钢卷号': '',
            '品名': '无花镀锌卷',
            '厂家': '攀华',
            '牌号': 'DX51D+Z',
            '规格型号': '0.75*1250*C',
            '数量(张)': '',
            '数量(卷)': 1,
            '重量(吨)': 5.250,
            '单价(元)': 0.00,
            '金额': 0.00,
            '备注': '分700+125*4+50'
        },
        {
            '钢卷号': '',
            '品名': '无花镀锌卷',
            '厂家': '攀华',
            '牌号': 'DX51D+Z',
            '规格型号': '0.75*1250*C',
            '数量(张)': '',
            '数量(卷)': 2,
            '重量(吨)': 11.450,
            '单价(元)': 0.00,
            '金额': 0.00,
            '备注': '分1100+50*3'
        }
    ]
    
    # 创建Excel文件
    with pd.ExcelWriter('武汉捷昇商贸送货单_25050716.xlsx', engine='openpyxl') as writer:
        
        # 创建送货单主表
        df_main = pd.DataFrame(goods_data)
        df_main.to_excel(writer, sheet_name='送货单明细', index=False, startrow=8)
        
        # 获取工作表对象
        worksheet = writer.sheets['送货单明细']
        
        # 设置表头信息
        worksheet['A1'] = '武汉捷昇商贸有限公司送货单'
        
        # 基本信息
        worksheet['A3'] = f"送货日期: {company_info['送货日期']}"
        worksheet['D3'] = f"送货单号: {company_info['送货单号']}"
        worksheet['G3'] = f"司机电话: {company_info['司机电话']}"
        worksheet['I3'] = f"NO: {company_info['单据编号']}"
        
        # 收货单位信息
        worksheet['A5'] = f"收货单位: {receiver_info['收货单位']}"
        worksheet['E5'] = f"收货地址: {receiver_info['收货地址']}"
        
        # 合计行
        total_weight = sum([item['重量(吨)'] for item in goods_data])
        total_rolls = sum([item['数量(卷)'] for item in goods_data])
        
        worksheet[f'A{8 + len(goods_data) + 2}'] = '合计:'
        worksheet[f'F{8 + len(goods_data) + 2}'] = 0  # 数量(张)
        worksheet[f'G{8 + len(goods_data) + 2}'] = total_rolls  # 数量(卷)
        worksheet[f'H{8 + len(goods_data) + 2}'] = total_weight  # 重量(吨)
        worksheet[f'I{8 + len(goods_data) + 2}'] = 0.00  # 单价
        
        # 大写重量
        worksheet[f'B{8 + len(goods_data) + 3}'] = '大写:'
        worksheet[f'C{8 + len(goods_data) + 3}'] = '零张'
        worksheet[f'D{8 + len(goods_data) + 3}'] = '陆卷'
        worksheet[f'E{8 + len(goods_data) + 3}'] = '叁拾肆点玖捌吨'
        worksheet[f'F{8 + len(goods_data) + 3}'] = '零元整'
        
        # 注意事项
        notes = [
            "注意：",
            "1. 产品的质量标准以国家标准为准确，以行业标准为准。",
            "2. 产品送到工厂后，外观、规格、检验，没有问题后方使用。有质量问题应在收货3日内提出，过期间不再负责。",
            "3. 质量问题的处理意见如期间内标准以厂家的意见标准为准。本公司只是协助客户联系厂家决策意见及赔偿，",
            "   客户不可以质量问题为由，扣款，拒收，拒交货款拒付本公司货款。",
            "4. 产品的质量以厂家的质量检验单为准，对于由客户产品制作的半成品、成品及损坏等，不负赔偿责任。",
            "5. 产品的重量以厂家的质量标准金额重量为准，允许3‰的重量误差，根据行业标准，正负3‰误差属于正常误差范围。"
        ]
        
        start_row = 8 + len(goods_data) + 5
        for i, note in enumerate(notes):
            worksheet[f'A{start_row + i}'] = note
        
        # 司机信息
        worksheet[f'A{start_row + len(notes) + 2}'] = '司机姓名：刘五洋'
        worksheet[f'D{start_row + len(notes) + 2}'] = '司机身份证号码：430682198705054417'
        worksheet[f'H{start_row + len(notes) + 2}'] = '收货人（签字）：'
        worksheet[f'I{start_row + len(notes) + 2}'] = '电话：'
        
        # 调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print("Excel文件已生成：武汉捷昇商贸送货单_25050716.xlsx")
    return "武汉捷昇商贸送货单_25050716.xlsx"

if __name__ == "__main__":
    create_delivery_note_excel()
