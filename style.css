/* --- 全局樣式與變數 --- */
:root {
    --primary-color: #0052D9;
    --dark-grey: #333;
    --medium-grey: #666;
    --light-grey: #f4f4f4;
    --text-color: #333;
    --background-color: #FFFFFF;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
    margin: 0;
    padding-top: 60px; /* 為固定導航欄預留空間 */
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

h1, h2, h3 {
    font-weight: 600;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

/* --- 1. 頂部導航欄 --- */
.main-header {
    background-color: var(--background-color);
    border-bottom: 1px solid #eee;
    padding: 0 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.main-header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
}

.main-header .logo img {
    height: 30px;
    vertical-align: middle;
}

.main-nav ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
}

.main-nav li {
    margin-left: 30px;
}

.main-nav a {
    color: var(--dark-grey);
    font-size: 16px;
    font-weight: 500;
    padding: 20px 0;
    position: relative;
}

.main-nav a:hover, .main-nav a.active {
    color: var(--primary-color);
}

.main-nav a.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
}

.header-right {
    display: flex;
    align-items: center;
}

.stock-info {
    font-size: 14px;
    color: var(--medium-grey);
    margin-right: 20px;
}

.search-icon, .lang-switch {
    font-size: 16px;
    margin-left: 20px;
    color: var(--dark-grey);
}

.mobile-menu-toggle {
    display: none;
    font-size: 24px;
    background: none;
    border: none;
    cursor: pointer;
}


/* --- 2. 主視覺橫幅 --- */
.hero-banner {
    background-image: url('https://www.tencent.com/img/index/p1_bg_3.jpg'); /* 替換為您自己的圖片URL */
    background-size: cover;
    background-position: center;
    height: 60vh;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
}

.hero-content h1 {
    font-size: 48px;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 20px;
    max-width: 600px;
    margin-bottom: 30px;
}

.btn {
    padding: 12px 25px;
    border-radius: 5px;
    font-weight: bold;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: #fff;
    color: var(--primary-color);
}

/* --- 3. 核心業務區塊 --- */
.features-section {
    padding: 80px 0;
    background-color: var(--light-grey);
}

.section-title {
    text-align: center;
    margin-bottom: 60px;
}

.section-title h2 {
    font-size: 36px;
    margin-bottom: 10px;
}

.section-title p {
    font-size: 18px;
    color: var(--medium-grey);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    text-align: center;
}

.feature-item .feature-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.feature-item h3 {
    font-size: 22px;
    margin-bottom: 10px;
}

.feature-item p {
    font-size: 16px;
    color: var(--medium-grey);
}

/* --- 4. 頁腳 --- */
.main-footer {
    background-color: #222;
    color: #ccc;
    padding: 60px 0 20px;
}

.footer-links {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.footer-column {
    flex: 1;
    min-width: 200px;
    margin-bottom: 20px;
}

.footer-column h4 {
    font-size: 18px;
    color: #fff;
    margin-bottom: 20px;
}

.footer-column a {
    display: block;
    margin-bottom: 10px;
    color: #ccc;
}

.footer-column a:hover {
    color: #fff;
}

.social-icons a {
    font-size: 24px;
    margin-right: 15px;
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 20px;
    text-align: center;
    font-size: 14px;
    color: #999;
}

.footer-bottom a {
    color: #999;
    margin: 0 10px;
}
.footer-bottom a:hover {
    color: #fff;
}


/* --- 響應式設計 --- */
@media (max-width: 768px) {
    body {
        padding-top: 60px;
    }
    
    .main-nav, .header-right .stock-info, .header-right .search-icon, .header-right .lang-switch {
        display: none;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-content h1 {
        font-size: 32px;
    }

    .hero-content p {
        font-size: 16px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-links {
        flex-direction: column;
        text-align: center;
    }
}