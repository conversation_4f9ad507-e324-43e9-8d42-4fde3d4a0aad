{"mcpServers": {"playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "description": "Playwright自动化测试工具，用于前端测试和页面操作"}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "description": "分步骤思维工具，用于复杂任务分解和决策支持"}, "mysql-mcp": {"command": "npx", "args": ["-y", "@f4ww4z/mcp-mysql-server"], "env": {"MYSQL_HOST": "127.0.0.1:3306", "MYSQL_USER": "root", "MYSQL_PASSWORD": "root", "MYSQL_DATABASE": "aigcview"}, "description": "MySQL数据库操作工具，用于数据库管理和查询"}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "description": "文档上下文集成工具，用于获取最新API文档和最佳实践"}, "browser": {"command": "npx", "args": ["@agentdeskai/browser-tools-mcp@latest"], "description": "浏览器工具，用于页面截图、调试和性能分析"}, "mcp-server-alipay": {"command": "cmd", "args": ["/c", "npx", "-y", "@alipay/mcp-server-alipay"], "env": {"AP_APP_ID": "2021005179622178", "AP_APP_KEY": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC2WyRKC/pxbb7f+/uqrbCM6rmUjrpmz9m63ZkORqseN5P4mpNqfmNxbjNmNHcwDvcLECLNx8/VoE2wsZe+JV/Z0gRSRYTDqdOnmx3dn5LSCT7OTIPsvhCp0hEfTC6/2mQWOn1J7VdTy9E3ql1PLNR3h7n8FC1mG5vHfXurt8rAVDh1RNw0spFI9VkB3qyy1btEAgOBdYF56bRJcs3MX+1BKNCY9kpRh3dyb7vVkSDJZTeMcgrQjkzkfF9vZvPu3b6CDicwwpiN9kXisoqX7mlopjUEoHMm991hy+ulxxDeQczhGSYT2gJCsfrNTdxdHt6UqILo8Liy1crAZGTvBa4zAgMBAAECggEAP9gfF3GkKExVFJRjYzlWY6/Wc4WZC1/gCgWFjbL+PCEXRGXSEOnZJSCBDFp4FDJZGd13YfZ/9HogWSmESAVLzrYx3yAJwHtX6Alt5mb0/2ZPP+kYEy3kJaCvb/a7M15hxkqKDPT0qxjj0gpqisrWRFcXrYLe9i3aV3i+cLtzV+xL/tThIh/7KSpNdaeIUAVU5VHxU5Xfvr+GuWzwEXHbbznHJJocxufpwn8M2biPlsXdKhhZouL0kLQRLld0zvhhTw/61ekmS9kIPOId7ArbgdfmTCbpYU2i5kjkIQkj/PVeZDTb4aJeQOeEKhnJOZQrVlFBFJdjzWddH/0TaC3Y4QKBgQD3U3zIC2lGyaXNwNbb3yoX0/dTQ+0/AAZcGEVjYCB5mMa+Aax0RoraDkJclid6Q0RKcBENmHCvJc5lQXM4N3JtJjSr/YfV385RuQnFz+mx1CDcjRSt7on5HGrNTmuWFh5MwrQU8FBrJLrntE8tCu2lQCovy3zmgxCwAoQdhNbtcQKBgQC8wFkOAKO/7J96S7Ji1XWma6XMcI0v+1opmbwIF0au1YtzQ1yb+yiFk9TWI/Yrv1CfouT7FebAae8raUonlCZDaE8OWj8CTzG3HKhB84Ll39aIybGIApW4ZvE6+KFjzAooo/nkBmz+8S7cAhFKkXiV9k9FSwj/mS41sYAn8A3T4wKBgQCuGyiRTm8q9NLyd9kI6R2hrobCJ+RAYE0Z0cAvB8F0uBbYNSdopywh+r2+nblmuSm+2Qs4LeLNeWjFIQDyRx4XNjkIBi70YVvFbeQyS32wwKrtd6diFbUtF1Mb+iOEgUeNM9c/kjiaZD3q1KSUCchndzb7dF0VjJhXvz2v5g7DEQKBgGVxIuuJoHgHnfZGNIs1mdNlS2hCnT7KYby16P6YM97G2fmKnH0gULjpqJumHCmK2rPRAiuHq8qBpI66OaZn0uCrcMGP8wk0ss1s02kOjK0qzcwaShWE/h5rBP7inSpbgKIy7pyEb5Pc34hSfCSNZjeCdzwQhbeJBfLJgMz2ERhtAoGBAIzG3y8ZwdZX+3dSb2bsfxiByA1lxDbpZGUKz0jk/B6MEgqMwDmBtalT+Y5jBUdvpNBexpGQ9bRKexZ5pDh1Fq0XoPcesIRNqDqkDTS8NGjsqsU9mlmQu7RFYWZbNcIyyDw+A8aLweMF7kG1wt7/RA7u9WCfxu4EhFwETy/gZ46l", "AP_PUB_KEY": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyqRCUsAfnioBBMK5lbyRTuPHTu1/cMP94OAX0jDabg3VOaItw6/Cnahu5SdMq7XBU2XKd1hGG/DF7Bx5oqA1fWSKZAP6b7UHfjjsLNmtIl+nVGNJiMI7bjHmzFF/CaKXBgONoBVAjTRza6WkpEY00vqJmbQ7yzd+Zk+px7dPNC0Z4GCTY2YBtp1RF3iDIE7WcL/B8JhZwxqzyibthrYAu0eGYGL97O74z7Mtut3cLDlM9/Gw2UYG0X/dHPoGVJE9kN7T9lRUy8zLTaW47In/O91T6uhBrVTYK8M8eDp5ldXCc1S+FeYAgGEtp8tdY3h2o793oRAxf5PYePJtdezf3QIDAQAB"}, "description": "支付宝MCP服务器，用于支付宝相关功能集成"}}}